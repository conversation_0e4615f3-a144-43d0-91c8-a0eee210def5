import Image from "next/image";
import Link from "next/link";
import ImageWithFallback from "@/components/ImageWithFallback";

export default function CourseCoach({ data }) {
  return (
    <div className="flex flex-col md:flex-row gap-8">
      <div className="w-40 m-auto md:m-0 border-2 border-sky-500 ">
        <Image
          src={data?.coach_id?.profileImg}
          priority
          width={500}
          height={500}
          alt="Coach Image"
          className="w-full h-40 object-cover"
        />
      </div>
      <div className="flex flex-col m-auto md:m-0 text-center md:text-left justify-between w-10/12 md:w-8/12 gap-4">
        <h2 className="text-lg text-black">{data.coachName}</h2>
        <h3 className="text-base text-sky-500">{data.category} Coach</h3>
        <p className="text-sm text-gray-400">
          {data.coachName} is well known for {data.category} in{" "}
          {data.facility.city} with {data?.coach_id?.experience} year&#39;s of
          experience in {data.category} coaching.
        </p>
        {data?.coach_id?.profileImg ? (
          <Link
            href={`/coaches/${data?.coach_id?._id}`}
            className="text-base flex items-center gap-2 underline m-auto md:m-0"
          >
            View Profile
            <Image
              src="/Arrow.svg"
              alt="Arrow"
              width={50}
              height={50}
              className="w-3 h-auto"
            />
          </Link>
        ) : (
          ""
        )}
      </div>
    </div>
  );
}
