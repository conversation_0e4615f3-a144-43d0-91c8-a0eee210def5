"use client";
import { useState } from "react";
import Image from "next/image";

export default function ImageWithFallback({
  src,
  fallbackSrc = "/default-user.svg",
  alt,
  width,
  height,
  className = "",
  priority = false,
  fill = false,
  sizes,
  ...props
}) {
  const [imgSrc, setImgSrc] = useState(src);
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    if (!hasError && imgSrc !== fallbackSrc) {
      setHasError(true);
      setImgSrc(fallbackSrc);
    }
  };

  // If no src provided, use fallback immediately
  if (!src || src === "" || src === null || src === undefined) {
    return (
      <Image
        src={fallbackSrc}
        alt={alt || "Default image"}
        width={width}
        height={height}
        className={className}
        priority={priority}
        fill={fill}
        sizes={sizes}
        {...props}
      />
    );
  }

  return (
    <Image
      src={imgSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      fill={fill}
      sizes={sizes}
      onError={handleError}
      {...props}
    />
  );
}
